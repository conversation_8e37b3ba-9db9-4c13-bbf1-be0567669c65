import axios from "axios";
import { Telegraf } from "telegraf";
import OpenAI from "openai";
import { RSI, EMA, WMA } from "technicalindicators";
import fs from "fs";
import dotenv from "dotenv";
import dayjs from "dayjs";

// Chart handler
import ChartHandler from "./src/handlers/Chart.js";

dotenv.config();

// === Config ===
const BINANCE_API = "https://api.binance.com/api/v3/klines";
const SYMBOL = process.env.SYMBOL || "ETHUSDT";
const INTERVAL = process.env.INTERVAL || "1h";
const bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN);
const chatId = process.env.TELEGRAM_GROUP_ID;
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Initialize Chart Handler
const chartHandler = new ChartHandler(SYMBOL, INTERVAL);

// === Multi-Timeframe Config ===
const TIMEFRAMES = {
  "15m": { interval: "15m", limit: 96, name: "15-minute" }, // 24 hours of 15m candles
  "1h": { interval: "1h", limit: 168, name: "1-hour" }, // 7 days of 1h candles
  "4h": { interval: "4h", limit: 180, name: "4-hour" }, // 30 days of 4h candles
};

// Data cache for multi-timeframe analysis
const dataCache = {
  "15m": { data: null, lastUpdate: 0, ttl: 15 * 60 * 1000 }, // 15 min TTL
  "1h": { data: null, lastUpdate: 0, ttl: 60 * 60 * 1000 }, // 1 hour TTL
  "4h": { data: null, lastUpdate: 0, ttl: 4 * 60 * 60 * 1000 }, // 4 hour TTL
};

// === Multi-Timeframe Data Fetching ===
async function fetchTimeframeData(timeframe) {
  const config = TIMEFRAMES[timeframe];
  if (!config) {
    throw new Error(`Invalid timeframe: ${timeframe}`);
  }

  try {
    const { data } = await axios.get(BINANCE_API, {
      params: {
        symbol: SYMBOL,
        interval: config.interval,
        limit: config.limit,
      },
    });

    return data.map((d) => ({
      time: d[0],
      open: parseFloat(d[1]),
      high: parseFloat(d[2]),
      low: parseFloat(d[3]),
      close: parseFloat(d[4]),
      volume: parseFloat(d[5]),
      closeTime: d[6],
      timeframe: timeframe,
    }));
  } catch (error) {
    console.error(`Error fetching ${timeframe} data:`, error.message);
    throw error;
  }
}

// === Cached Data Fetching ===
async function getCachedData(timeframe) {
  const cache = dataCache[timeframe];
  const now = Date.now();

  // Check if cache is valid
  if (cache.data && now - cache.lastUpdate < cache.ttl) {
    console.log(`Using cached data for ${timeframe}`);
    return cache.data;
  }

  // Fetch fresh data
  console.log(`Fetching fresh data for ${timeframe}`);
  const data = await fetchTimeframeData(timeframe);

  // Update cache
  cache.data = data;
  cache.lastUpdate = now;

  return data;
}

// === Fetch All Timeframes ===
async function fetchAllTimeframes() {
  try {
    const [data15m, data1h, data4h] = await Promise.all([
      getCachedData("15m"),
      getCachedData("1h"),
      getCachedData("4h"),
    ]);

    return {
      "15m": data15m,
      "1h": data1h,
      "4h": data4h,
    };
  } catch (error) {
    console.error("Error fetching multi-timeframe data:", error);
    throw error;
  }
}

// === Legacy fetchData function (for backward compatibility) ===
async function fetchData() {
  const { data } = await axios.get(BINANCE_API, {
    params: { symbol: SYMBOL, interval: INTERVAL, limit: 240 },
  });

  return data.map((d) => ({
    time: d[0],
    open: parseFloat(d[1]),
    high: parseFloat(d[2]),
    low: parseFloat(d[3]),
    close: parseFloat(d[4]),
    volume: parseFloat(d[5]),
    closeTime: d[6],
  }));
}

// === Indicators (EMA, Sonic R PAC, RSI + EMA9 + WMA45) ===
function padToLen(arr, len) {
  return Array(len - arr.length)
    .fill(null)
    .concat(arr);
}

function calculateIndicators(candles) {
  const closes = candles.map((c) => c.close);
  const highs = candles.map((c) => c.high);
  const lows = candles.map((c) => c.low);
  const len = candles.length;

  // Sonic R PAC (EMA 34 của high/low/close)
  const pacLen = 34;
  const pacC = padToLen(EMA.calculate({ values: closes, period: pacLen }), len);
  const pacH = padToLen(EMA.calculate({ values: highs, period: pacLen }), len);
  const pacL = padToLen(EMA.calculate({ values: lows, period: pacLen }), len);

  // EMA
  const ema20 = padToLen(EMA.calculate({ values: closes, period: 20 }), len);
  const ema50 = padToLen(EMA.calculate({ values: closes, period: 50 }), len);
  const ema89 = padToLen(EMA.calculate({ values: closes, period: 89 }), len);
  const ema200 = padToLen(EMA.calculate({ values: closes, period: 200 }), len);
  const ema610 = padToLen(EMA.calculate({ values: closes, period: 610 }), len);

  // RSI + EMA9 + WMA45
  const rsi14 = RSI.calculate({ values: closes, period: 14 });
  const rsi = padToLen(rsi14, len);
  const rsiEma9 = padToLen(EMA.calculate({ values: rsi14, period: 9 }), len);
  const rsiWma45 = padToLen(WMA.calculate({ values: rsi14, period: 45 }), len);

  return {
    closes,
    pacC,
    pacH,
    pacL,
    ema20,
    ema50,
    ema89,
    ema200,
    ema610,
    rsi,
    rsiEma9,
    rsiWma45,
  };
}

// === Trend Analysis Engine ===
function analyzeTrend(candles, indicators) {
  if (!candles || candles.length < 50) {
    return { trend: "INSUFFICIENT_DATA", strength: 0, confidence: 0 };
  }

  const { ema20, ema50, ema89, ema200, closes, rsi } = indicators;
  const currentPrice = closes[closes.length - 1];
  const currentEma20 = ema20[ema20.length - 1];
  const currentEma50 = ema50[ema50.length - 1];
  const currentEma89 = ema89[ema89.length - 1];
  const currentEma200 = ema200[ema200.length - 1];
  const currentRsi = rsi[rsi.length - 1];

  // EMA alignment analysis
  const emaAlignment = analyzeEmaAlignment(
    currentPrice,
    currentEma20,
    currentEma50,
    currentEma89,
    currentEma200
  );

  // Price action analysis
  const priceAction = analyzePriceAction(candles.slice(-20)); // Last 20 candles

  // RSI momentum analysis
  const rsiMomentum = analyzeRsiMomentum(currentRsi);

  // Volume analysis
  const volumeAnalysis = analyzeVolume(candles.slice(-10)); // Last 10 candles

  // Combine all factors to determine trend
  const trendScore = calculateTrendScore(
    emaAlignment,
    priceAction,
    rsiMomentum,
    volumeAnalysis
  );

  return {
    trend: determineTrend(trendScore.score),
    strength: Math.abs(trendScore.score),
    confidence: trendScore.confidence,
    details: {
      emaAlignment,
      priceAction,
      rsiMomentum,
      volumeAnalysis,
      score: trendScore.score,
    },
  };
}

function analyzeEmaAlignment(price, ema20, ema50, ema89, ema200) {
  let score = 0;
  let alignmentCount = 0;

  // Check EMA hierarchy for bullish/bearish alignment
  if (ema20 > ema50) {
    score += 1;
    alignmentCount++;
  }
  if (ema50 > ema89) {
    score += 1;
    alignmentCount++;
  }
  if (ema89 > ema200) {
    score += 1;
    alignmentCount++;
  }
  if (price > ema20) {
    score += 2;
    alignmentCount += 2;
  } // Price above short EMA is more important

  // Check for bearish alignment
  if (ema20 < ema50) {
    score -= 1;
    alignmentCount++;
  }
  if (ema50 < ema89) {
    score -= 1;
    alignmentCount++;
  }
  if (ema89 < ema200) {
    score -= 1;
    alignmentCount++;
  }
  if (price < ema20) {
    score -= 2;
    alignmentCount += 2;
  }

  return {
    score: alignmentCount > 0 ? score / alignmentCount : 0,
    strength: Math.abs(score) / Math.max(alignmentCount, 1),
  };
}

function analyzePriceAction(recentCandles) {
  if (recentCandles.length < 10)
    return { score: 0, pattern: "INSUFFICIENT_DATA" };

  let higherHighs = 0;
  let lowerLows = 0;
  let bullishCandles = 0;
  let bearishCandles = 0;

  for (let i = 1; i < recentCandles.length; i++) {
    const current = recentCandles[i];
    const previous = recentCandles[i - 1];

    // Count higher highs and lower lows
    if (current.high > previous.high) higherHighs++;
    if (current.low < previous.low) lowerLows++;

    // Count bullish/bearish candles
    if (current.close > current.open) bullishCandles++;
    if (current.close < current.open) bearishCandles++;
  }

  const totalCandles = recentCandles.length - 1;
  const hhPercentage = higherHighs / totalCandles;
  const llPercentage = lowerLows / totalCandles;
  const bullishPercentage = bullishCandles / totalCandles;

  let score = 0;
  let pattern = "SIDEWAYS";

  if (hhPercentage > 0.6 && bullishPercentage > 0.6) {
    score = (hhPercentage + bullishPercentage) / 2;
    pattern = "BULLISH_MOMENTUM";
  } else if (llPercentage > 0.6 && bullishPercentage < 0.4) {
    score = -((llPercentage + (1 - bullishPercentage)) / 2);
    pattern = "BEARISH_MOMENTUM";
  }

  return {
    score,
    pattern,
    higherHighs,
    lowerLows,
    bullishCandles,
    bearishCandles,
  };
}

function analyzeRsiMomentum(rsi) {
  if (!rsi || rsi < 0 || rsi > 100) return { score: 0, condition: "INVALID" };

  let score = 0;
  let condition = "NEUTRAL";

  if (rsi > 70) {
    score = -0.5; // Overbought - potential bearish
    condition = "OVERBOUGHT";
  } else if (rsi < 30) {
    score = 0.5; // Oversold - potential bullish
    condition = "OVERSOLD";
  } else if (rsi > 50) {
    score = (rsi - 50) / 100; // Bullish momentum
    condition = "BULLISH_MOMENTUM";
  } else {
    score = (rsi - 50) / 100; // Bearish momentum
    condition = "BEARISH_MOMENTUM";
  }

  return { score, condition, value: rsi };
}

function analyzeVolume(recentCandles) {
  if (recentCandles.length < 5) return { score: 0, trend: "INSUFFICIENT_DATA" };

  const volumes = recentCandles.map((c) => c.volume);
  const avgVolume = volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length;
  const recentVolume = volumes[volumes.length - 1];

  let score = 0;
  let trend = "NORMAL";

  if (recentVolume > avgVolume * 1.5) {
    score = 0.3; // High volume supports trend
    trend = "HIGH_VOLUME";
  } else if (recentVolume < avgVolume * 0.5) {
    score = -0.1; // Low volume weakens trend
    trend = "LOW_VOLUME";
  }

  return { score, trend, current: recentVolume, average: avgVolume };
}

function calculateTrendScore(
  emaAlignment,
  priceAction,
  rsiMomentum,
  volumeAnalysis
) {
  // Weighted combination of all factors
  const weights = {
    ema: 0.4, // EMA alignment is most important
    price: 0.3, // Price action is second
    rsi: 0.2, // RSI momentum
    volume: 0.1, // Volume confirmation
  };

  const score =
    emaAlignment.score * weights.ema +
    priceAction.score * weights.price +
    rsiMomentum.score * weights.rsi +
    volumeAnalysis.score * weights.volume;

  // Calculate confidence based on alignment of factors
  const factors = [emaAlignment.score, priceAction.score, rsiMomentum.score];
  const avgFactor = factors.reduce((sum, f) => sum + f, 0) / factors.length;
  const variance =
    factors.reduce((sum, f) => sum + Math.pow(f - avgFactor, 2), 0) /
    factors.length;
  const confidence = Math.max(0, Math.min(1, 1 - variance)); // Lower variance = higher confidence

  return { score, confidence };
}

function determineTrend(score) {
  if (score > 0.3) return "BULLISH";
  if (score < -0.3) return "BEARISH";
  return "SIDEWAYS";
}

// === Multi-Timeframe Analysis ===
async function performMultiTimeframeAnalysis() {
  try {
    // Fetch all timeframe data
    const allData = await fetchAllTimeframes();

    // Calculate indicators for each timeframe
    const analysis = {};
    for (const [timeframe, candles] of Object.entries(allData)) {
      const indicators = calculateIndicators(candles);
      const trend = analyzeTrend(candles, indicators);

      analysis[timeframe] = {
        candles,
        indicators,
        trend,
        timeframeName: TIMEFRAMES[timeframe].name,
      };
    }

    // Add risk levels to 15m analysis
    if (analysis["15m"] && analysis["1h"]) {
      const majorTrend = { trend: "BULLISH", strength: 0.5 }; // Temporary for risk calculation
      analysis["15m"].riskLevels = calculateRiskLevels(
        analysis["15m"].candles,
        analysis["1h"].candles,
        majorTrend
      );
    }

    return analysis;
  } catch (error) {
    console.error("Error in multi-timeframe analysis:", error);
    throw error;
  }
}

// === Hierarchical Trading Logic ===
function generateTradingSignal(multiTimeframeAnalysis) {
  const { "4h": tf4h, "1h": tf1h, "15m": tf15m } = multiTimeframeAnalysis;

  // Step 1: Determine major trend from 4H and 1H
  const majorTrend = determineMajorTrend(tf4h.trend, tf1h.trend);

  // Step 2: Check for trend alignment
  const trendAlignment = checkTrendAlignment(
    tf4h.trend,
    tf1h.trend,
    tf15m.trend
  );

  // Step 3: Generate entry signal based on 15M timeframe
  const entrySignal = generateEntrySignal(tf15m, majorTrend);

  // Step 4: Calculate risk management levels
  const riskLevels = calculateRiskLevels(
    tf15m.candles,
    tf1h.candles,
    majorTrend
  );

  return {
    majorTrend,
    trendAlignment,
    entrySignal,
    riskLevels,
    confidence: calculateOverallConfidence(tf4h.trend, tf1h.trend, tf15m.trend),
    recommendation: generateTradeRecommendation(
      majorTrend,
      trendAlignment,
      entrySignal
    ),
  };
}

function determineMajorTrend(trend4h, trend1h) {
  // 4H trend has higher weight than 1H
  const weights = { "4h": 0.7, "1h": 0.3 };

  // Convert trends to numeric scores
  const trendToScore = { BULLISH: 1, SIDEWAYS: 0, BEARISH: -1 };

  const score4h = trendToScore[trend4h.trend] || 0;
  const score1h = trendToScore[trend1h.trend] || 0;

  const weightedScore = score4h * weights["4h"] + score1h * weights["1h"];

  let majorTrend = "SIDEWAYS";
  if (weightedScore > 0.3) majorTrend = "BULLISH";
  else if (weightedScore < -0.3) majorTrend = "BEARISH";

  return {
    trend: majorTrend,
    strength: Math.abs(weightedScore),
    components: {
      "4h": {
        trend: trend4h.trend,
        strength: trend4h.strength,
        confidence: trend4h.confidence,
      },
      "1h": {
        trend: trend1h.trend,
        strength: trend1h.strength,
        confidence: trend1h.confidence,
      },
    },
  };
}

function checkTrendAlignment(trend4h, trend1h, trend15m) {
  const trends = [trend4h.trend, trend1h.trend, trend15m.trend];
  const bullishCount = trends.filter((t) => t === "BULLISH").length;
  const bearishCount = trends.filter((t) => t === "BEARISH").length;
  const sidewaysCount = trends.filter((t) => t === "SIDEWAYS").length;

  let alignment = "MIXED";
  let strength = 0;

  if (bullishCount >= 2) {
    alignment = "BULLISH_ALIGNED";
    strength = bullishCount / 3;
  } else if (bearishCount >= 2) {
    alignment = "BEARISH_ALIGNED";
    strength = bearishCount / 3;
  } else if (sidewaysCount >= 2) {
    alignment = "SIDEWAYS_ALIGNED";
    strength = sidewaysCount / 3;
  }

  return {
    alignment,
    strength,
    details: {
      bullish: bullishCount,
      bearish: bearishCount,
      sideways: sidewaysCount,
    },
  };
}

function generateEntrySignal(tf15m, majorTrend) {
  const { trend, indicators, candles } = tf15m;
  const currentPrice = candles[candles.length - 1].close;
  const ema20 = indicators.ema20[indicators.ema20.length - 1];
  const ema50 = indicators.ema50[indicators.ema50.length - 1];
  const rsi = indicators.rsi[indicators.rsi.length - 1];

  let signal = "NO_SIGNAL";
  let entryPrice = null;
  let reasoning = [];

  // Only generate signals that align with major trend
  if (majorTrend.trend === "BULLISH" && trend.trend !== "BEARISH") {
    // Look for bullish entry conditions
    if (currentPrice > ema20 && ema20 > ema50 && rsi > 40 && rsi < 70) {
      signal = "LONG";
      entryPrice = currentPrice;
      reasoning.push(
        "Price above EMA20",
        "EMA20 > EMA50",
        "RSI in bullish range"
      );
    } else if (currentPrice < ema20 && currentPrice > ema50 && rsi < 50) {
      signal = "LONG_PULLBACK";
      entryPrice = ema20; // Wait for pullback to EMA20
      reasoning.push("Pullback to EMA20 in uptrend", "RSI oversold");
    }
  } else if (majorTrend.trend === "BEARISH" && trend.trend !== "BULLISH") {
    // Look for bearish entry conditions
    if (currentPrice < ema20 && ema20 < ema50 && rsi < 60 && rsi > 30) {
      signal = "SHORT";
      entryPrice = currentPrice;
      reasoning.push(
        "Price below EMA20",
        "EMA20 < EMA50",
        "RSI in bearish range"
      );
    } else if (currentPrice > ema20 && currentPrice < ema50 && rsi > 50) {
      signal = "SHORT_PULLBACK";
      entryPrice = ema20; // Wait for pullback to EMA20
      reasoning.push("Pullback to EMA20 in downtrend", "RSI overbought");
    }
  }

  return {
    signal,
    entryPrice,
    reasoning,
    currentPrice,
    keyLevels: {
      ema20,
      ema50,
      rsi,
    },
  };
}

function calculateRiskLevels(candles15m, candles1h, majorTrend) {
  const current15m = candles15m[candles15m.length - 1];
  const recent15m = candles15m.slice(-20); // Last 20 candles for support/resistance
  const recent1h = candles1h.slice(-10); // Last 10 1H candles for major levels

  // Calculate support and resistance levels
  const support = findSupportLevel(recent15m, recent1h);
  const resistance = findResistanceLevel(recent15m, recent1h);

  // Calculate stop loss and take profit based on trend
  let stopLoss, takeProfit1, takeProfit2;
  const atr = calculateATR(recent15m); // Average True Range for volatility

  if (majorTrend.trend === "BULLISH") {
    stopLoss = Math.max(support, current15m.close - atr * 2);
    takeProfit1 = current15m.close + atr * 2;
    takeProfit2 = Math.min(resistance, current15m.close + atr * 4);
  } else if (majorTrend.trend === "BEARISH") {
    stopLoss = Math.min(resistance, current15m.close + atr * 2);
    takeProfit1 = current15m.close - atr * 2;
    takeProfit2 = Math.max(support, current15m.close - atr * 4);
  } else {
    // Sideways market - tighter stops
    stopLoss = current15m.close + (majorTrend.trend === "BULLISH" ? -atr : atr);
    takeProfit1 =
      current15m.close + (majorTrend.trend === "BULLISH" ? atr : -atr);
    takeProfit2 = takeProfit1;
  }

  return {
    support,
    resistance,
    stopLoss,
    takeProfit1,
    takeProfit2,
    atr,
    riskReward:
      Math.abs(takeProfit1 - current15m.close) /
      Math.abs(stopLoss - current15m.close),
  };
}

function findSupportLevel(candles15m, candles1h) {
  const lows15m = candles15m.map((c) => c.low);
  const lows1h = candles1h.map((c) => c.low);

  // Find recent significant lows
  const support15m = Math.min(...lows15m.slice(-10));
  const support1h = Math.min(...lows1h.slice(-5));

  return Math.min(support15m, support1h);
}

function findResistanceLevel(candles15m, candles1h) {
  const highs15m = candles15m.map((c) => c.high);
  const highs1h = candles1h.map((c) => c.high);

  // Find recent significant highs
  const resistance15m = Math.max(...highs15m.slice(-10));
  const resistance1h = Math.max(...highs1h.slice(-5));

  return Math.max(resistance15m, resistance1h);
}

function calculateATR(candles, period = 14) {
  if (candles.length < period + 1) return 0;

  const trueRanges = [];
  for (let i = 1; i < candles.length; i++) {
    const current = candles[i];
    const previous = candles[i - 1];

    const tr = Math.max(
      current.high - current.low,
      Math.abs(current.high - previous.close),
      Math.abs(current.low - previous.close)
    );
    trueRanges.push(tr);
  }

  // Calculate average of last 'period' true ranges
  const recentTR = trueRanges.slice(-period);
  return recentTR.reduce((sum, tr) => sum + tr, 0) / recentTR.length;
}

function calculateOverallConfidence(trend4h, trend1h, trend15m) {
  const confidences = [
    trend4h.confidence,
    trend1h.confidence,
    trend15m.confidence,
  ];
  const avgConfidence =
    confidences.reduce((sum, c) => sum + c, 0) / confidences.length;

  // Boost confidence if trends align
  const trends = [trend4h.trend, trend1h.trend, trend15m.trend];
  const uniqueTrends = [...new Set(trends)];
  const alignmentBonus =
    uniqueTrends.length === 1 ? 0.2 : uniqueTrends.length === 2 ? 0.1 : 0;

  return Math.min(1, avgConfidence + alignmentBonus);
}

function generateTradeRecommendation(majorTrend, trendAlignment, entrySignal) {
  let recommendation = "NO_TRADE";
  let reasoning = [];

  // High confidence trades
  if (
    trendAlignment.alignment.includes("ALIGNED") &&
    entrySignal.signal !== "NO_SIGNAL"
  ) {
    if (majorTrend.trend === "BULLISH" && entrySignal.signal.includes("LONG")) {
      recommendation = "STRONG_LONG";
      reasoning.push(
        "Multi-timeframe bullish alignment",
        "Clear long entry signal"
      );
    } else if (
      majorTrend.trend === "BEARISH" &&
      entrySignal.signal.includes("SHORT")
    ) {
      recommendation = "STRONG_SHORT";
      reasoning.push(
        "Multi-timeframe bearish alignment",
        "Clear short entry signal"
      );
    }
  }
  // Medium confidence trades
  else if (majorTrend.strength > 0.5 && entrySignal.signal !== "NO_SIGNAL") {
    if (majorTrend.trend === "BULLISH" && entrySignal.signal.includes("LONG")) {
      recommendation = "MODERATE_LONG";
      reasoning.push("Strong major trend", "Partial alignment");
    } else if (
      majorTrend.trend === "BEARISH" &&
      entrySignal.signal.includes("SHORT")
    ) {
      recommendation = "MODERATE_SHORT";
      reasoning.push("Strong major trend", "Partial alignment");
    }
  }
  // Wait for better setup
  else {
    recommendation = "WAIT";
    reasoning.push("Mixed signals", "Wait for better alignment");
  }

  return {
    action: recommendation,
    reasoning,
    confidence: calculateTradeConfidence(
      majorTrend,
      trendAlignment,
      entrySignal
    ),
  };
}

function calculateTradeConfidence(majorTrend, trendAlignment, entrySignal) {
  let confidence = 0;

  // Major trend strength
  confidence += majorTrend.strength * 0.4;

  // Trend alignment
  confidence += trendAlignment.strength * 0.3;

  // Entry signal quality
  if (entrySignal.signal !== "NO_SIGNAL") {
    confidence += 0.2;
    if (entrySignal.reasoning.length >= 2) confidence += 0.1;
  }

  return Math.min(1, confidence);
}

// === Helpers ===
function inferTimeUnit(interval) {
  const s = interval.toLowerCase();
  if (s.endsWith("m")) return "minute";
  if (s.endsWith("h")) return "hour";
  if (s.endsWith("d")) return "day";
  return "hour";
}

// === Enhanced Multi-Timeframe GPT Analysis ===
async function analyzeWithGPT(multiTimeframeAnalysis, tradingSignal) {
  const { "15m": tf15m, "1h": tf1h, "4h": tf4h } = multiTimeframeAnalysis;
  const last15m = tf15m.candles.at(-1);

  // Format trend information
  const formatTrend = (trend) => {
    const trendEmoji = { BULLISH: "🟢", BEARISH: "🔴", SIDEWAYS: "⚪" };
    return `${trendEmoji[trend.trend] || "⚪"} ${trend.trend} (${(
      trend.strength * 100
    ).toFixed(0)}%)`;
  };

  // Format trading signal
  const formatSignal = (signal) => {
    const signalEmoji = {
      STRONG_LONG: "🟢🟢",
      MODERATE_LONG: "🟢",
      STRONG_SHORT: "🔴🔴",
      MODERATE_SHORT: "🔴",
      WAIT: "⚪",
      NO_TRADE: "⚪",
    };
    return `${signalEmoji[signal.action] || "⚪"} ${signal.action}`;
  };

  const text = `
You are a professional trading analyst. Analyze ${SYMBOL} using multi-timeframe analysis:

MULTI-TIMEFRAME TRENDS:
- 4H Trend: ${formatTrend(tf4h.trend)} (Confidence: ${(
    tf4h.trend.confidence * 100
  ).toFixed(0)}%)
- 1H Trend: ${formatTrend(tf1h.trend)} (Confidence: ${(
    tf1h.trend.confidence * 100
  ).toFixed(0)}%)
- 15M Trend: ${formatTrend(tf15m.trend)} (Confidence: ${(
    tf15m.trend.confidence * 100
  ).toFixed(0)}%)

MAJOR TREND: ${formatTrend(tradingSignal.majorTrend)} (Strength: ${(
    tradingSignal.majorTrend.strength * 100
  ).toFixed(0)}%)
ALIGNMENT: ${tradingSignal.trendAlignment.alignment} (${(
    tradingSignal.trendAlignment.strength * 100
  ).toFixed(0)}%)

CURRENT DATA (15M):
- Price: ${last15m.close}
- EMA20/50/89: ${[
    tf15m.indicators.ema20.at(-1),
    tf15m.indicators.ema50.at(-1),
    tf15m.indicators.ema89.at(-1),
  ]
    .map((v) => v?.toFixed(2))
    .join("/")}
- RSI: ${tf15m.indicators.rsi.at(-1)?.toFixed(1)}
- Volume: ${last15m.volume}

TRADING SIGNAL: ${formatSignal(tradingSignal.recommendation)}
ENTRY SIGNAL: ${tradingSignal.entrySignal.signal}
RISK LEVELS: SL ${tradingSignal.riskLevels.stopLoss?.toFixed(
    2
  )} | TP1 ${tradingSignal.riskLevels.takeProfit1?.toFixed(
    2
  )} | RR ${tradingSignal.riskLevels.riskReward?.toFixed(2)}

RESPOND in this EXACT HTML format:

<b>📊 MULTI-TIMEFRAME ANALYSIS:</b>
• <b>Major Trend:</b> [4H+1H combined trend] - [Key reason]
• <b>Alignment:</b> [Timeframe alignment status]
• <b>15M Entry:</b> [Current 15M setup]

<b>🎯 TRADING RECOMMENDATION:</b>
• <b>Action:</b> [${tradingSignal.recommendation.action}]
• <b>Entry:</b> [Specific entry price/condition]
• <b>Stop Loss:</b> [SL level] | <b>Take Profit:</b> [TP levels]
• <b>Risk/Reward:</b> 1:${tradingSignal.riskLevels.riskReward?.toFixed(1)}

<b>💡 Confidence:</b> ${(tradingSignal.confidence * 100).toFixed(0)}%

IMPORTANT: Use only <b></b> HTML tags, no markdown. Be specific with prices. Max 500 characters.`;

  const res = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      {
        role: "system",
        content:
          "You are a professional trading analyst. MUST respond in EXACT HTML format requested. Use only <b></b> tags, no markdown. Be concise and professional.",
      },
      { role: "user", content: text },
    ],
  });

  return res.choices[0].message.content;
}

// === Legacy GPT Analysis (for backward compatibility) ===
async function legacyAnalyzeWithGPT(candles, ind) {
  const last = candles.at(-1);

  const text = `
Bạn là chuyên gia phân tích kỹ thuật. Phân tích ${SYMBOL} khung ${INTERVAL} với dữ liệu:
- Giá hiện tại: ${last.close}
- EMA20/50/89: ${[ind.ema20.at(-1), ind.ema50.at(-1), ind.ema89.at(-1)]
    .map((v) => v?.toFixed(2))
    .join("/")}
- RSI(14): ${ind.rsi.at(-1)?.toFixed(1)}
- Sonic R PAC: ${ind.pacC.at(-1)?.toFixed(2)}
- Volume: ${last.volume}

BẮT BUỘC trả lời theo format HTML chính xác sau:

<b>📈 XU HƯỚNG:</b> [Tăng/Giảm/Sideway] - [Lý do ngắn gọn]

<b>🎯 KỊCH BẢN TRADE:</b>
• <b>Loại:</b> [LONG 🟢 / SHORT 🔴 / NO TRADE ⚪]
• <b>Setup:</b> [Điều kiện entry cụ thể]
• <b>Xác nhận:</b> [Tín hiệu cần chờ]
• <b>Entry/SL/TP:</b> Entry [giá], SL [giá], TP [giá] (RR 1:[tỷ lệ])
• <b>Invalidation:</b> [Điều kiện hủy kèo]

<b>💡 Độ tin cậy:</b> [X]%

QUAN TRỌNG:
- Chỉ sử dụng thẻ HTML <b> và </b>
- Không dùng **, ##, hay markdown khác
- Giá phải cụ thể, không mơ hồ
- Giới hạn 600 ký tự`;

  const res = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      {
        role: "system",
        content:
          "Bạn là chuyên gia trading. BẮT BUỘC trả lời theo CHÍNH XÁC format HTML được yêu cầu. Chỉ sử dụng thẻ <b></b>, không dùng ** hay markdown. Ngắn gọn, chuyên nghiệp.",
      },
      { role: "user", content: text },
    ],
  });

  return res.choices[0].message.content;
}

// === AI Vision-Powered Trading Bot ===
async function runBot() {
  try {
    console.log("🤖 Starting AI vision-powered trading analysis...");

    // Step 1: Perform multi-timeframe data collection
    const multiTimeframeAnalysis = await performMultiTimeframeAnalysis();

    // Step 2: AI Visual Analysis - Replace rule-based logic with AI vision
    const aiAnalysis = await performAIVisualAnalysis(multiTimeframeAnalysis);

    // Step 3: Generate AI-powered trading signal
    const aiTradingSignal = await generateAITradingSignal(
      aiAnalysis,
      multiTimeframeAnalysis
    );

    // Step 4: Create traditional chart for Telegram (15M with indicators)
    const candles15m = multiTimeframeAnalysis["15m"].candles;
    const indicators15m = multiTimeframeAnalysis["15m"].indicators;

    const chartPath = await chartHandler.generateCompleteChart(
      candles15m,
      indicators15m,
      multiTimeframeAnalysis,
      "chart.png"
    );

    // Step 5: Enhanced GPT analysis with AI vision results
    const analysis = await analyzeWithAIVision(aiAnalysis, aiTradingSignal);

    // Step 6: Create comprehensive caption with AI insights
    const caption = createAIEnhancedCaption(aiTradingSignal, analysis);

    // Step 7: Send message with chart and AI analysis
    await bot.telegram.sendPhoto(
      chatId,
      { source: chartPath },
      {
        caption,
        parse_mode: "HTML",
      }
    );

    console.log("✅ AI vision analysis completed successfully");
    console.log(
      `📊 AI Recommendation: ${aiTradingSignal.trading_recommendation.action}`
    );
    console.log(
      `🎯 AI Confidence: ${aiTradingSignal.overall_assessment.final_confidence}/10`
    );
  } catch (error) {
    console.error("❌ Error in AI vision bot:", error);

    // Fallback to rule-based multi-timeframe analysis
    try {
      console.log("🔄 Falling back to rule-based analysis...");
      await runRuleBasedBot();
    } catch (fallbackError) {
      console.error("❌ Rule-based fallback also failed:", fallbackError);

      // Final fallback to legacy single-timeframe
      try {
        console.log("🔄 Final fallback to legacy analysis...");
        await runLegacyBot();
      } catch (legacyError) {
        console.error("❌ All systems failed:", legacyError);

        // Send error notification
        await bot.telegram.sendMessage(
          chatId,
          `⚠️ <b>Critical Bot Error</b>\n\nAll analysis systems failed. Manual intervention required.\n\nPrimary Error: ${error.message}`,
          { parse_mode: "HTML" }
        );
      }
    }
  }
}

function createEnhancedCaption(
  multiTimeframeAnalysis,
  tradingSignal,
  analysis
) {
  const { majorTrend, trendAlignment, confidence } = tradingSignal;

  // Trend alignment indicators
  const alignmentEmoji = {
    BULLISH_ALIGNED: "🟢🟢🟢",
    BEARISH_ALIGNED: "🔴🔴🔴",
    SIDEWAYS_ALIGNED: "⚪⚪⚪",
    MIXED: "🟢🔴⚪",
  };

  const header = `📊 <b>${SYMBOL} - Multi-Timeframe Analysis</b>\n`;
  const alignment = `${
    alignmentEmoji[trendAlignment.alignment] || "⚪"
  } <b>Alignment:</b> ${trendAlignment.alignment} (${(confidence * 100).toFixed(
    0
  )}%)\n`;
  const majorTrendInfo = `📈 <b>Major Trend:</b> ${majorTrend.trend} (${(
    majorTrend.strength * 100
  ).toFixed(0)}%)\n\n`;

  return header + alignment + majorTrendInfo + analysis;
}

// === AI-Enhanced Caption Creation ===
function createAIEnhancedCaption(aiTradingSignal, analysis) {
  const { major_trend, trading_recommendation, overall_assessment } =
    aiTradingSignal;

  // AI confidence indicators
  const confidenceEmoji = {
    10: "🟢🟢🟢",
    9: "🟢🟢🟢",
    8: "🟢🟢",
    7: "🟢🟢",
    6: "🟢",
    5: "🟡",
    4: "🟡",
    3: "🟠",
    2: "🔴",
    1: "🔴",
  };

  const confidence = overall_assessment.final_confidence;
  const confidenceIndicator = confidenceEmoji[confidence] || "⚪";

  const header = `🤖 <b>${SYMBOL} - AI Vision Analysis</b>\n`;
  const aiConfidence = `${confidenceIndicator} <b>AI Confidence:</b> ${confidence}/10 (${overall_assessment.trade_quality})\n`;
  const recommendation = `🎯 <b>AI Signal:</b> ${trading_recommendation.action}\n\n`;

  return header + aiConfidence + recommendation + analysis;
}

// === Rule-Based Fallback Bot ===
async function runRuleBasedBot() {
  console.log("🔄 Running rule-based multi-timeframe analysis...");

  // Perform multi-timeframe analysis
  const multiTimeframeAnalysis = await performMultiTimeframeAnalysis();

  // Generate trading signal using rule-based logic
  const tradingSignal = generateTradingSignal(multiTimeframeAnalysis);

  // Use 15M data for chart rendering
  const candles15m = multiTimeframeAnalysis["15m"].candles;
  const indicators15m = multiTimeframeAnalysis["15m"].indicators;

  // Render charts using Chart handler
  const chartPath = await chartHandler.generateCompleteChart(
    candles15m,
    indicators15m,
    multiTimeframeAnalysis,
    "chart.png"
  );

  // Rule-based GPT analysis
  const analysis = await analyzeWithGPT(multiTimeframeAnalysis, tradingSignal);

  // Create caption
  const caption = createEnhancedCaption(
    multiTimeframeAnalysis,
    tradingSignal,
    analysis
  );

  // Send message
  await bot.telegram.sendPhoto(
    chatId,
    { source: chartPath },
    {
      caption,
      parse_mode: "HTML",
    }
  );

  console.log("✅ Rule-based analysis completed successfully");
}

// === Legacy Bot Function (fallback) ===
async function runLegacyBot() {
  const candles = await fetchData();
  const ind = calculateIndicators(candles);

  // render 3 panel using Chart handler
  const chartPath = await chartHandler.generateCompleteChart(
    candles,
    ind,
    null,
    "chart.png"
  );

  // analysis
  const analysis = await legacyAnalyzeWithGPT(candles, ind);

  // Send 1 message: photo + caption with HTML formatting
  await bot.telegram.sendPhoto(
    chatId,
    { source: chartPath },
    {
      caption: `📊 <b>${SYMBOL} (${INTERVAL})</b>\n\n${analysis}`,
      parse_mode: "HTML",
    }
  );
}

// === Run every hour ===
setInterval(runBot, 60 * 60 * 1000);
runBot();

// === Exports for Testing ===
export {
  // Data functions
  fetchAllTimeframes,
  performMultiTimeframeAnalysis,
  calculateIndicators,
  analyzeTrend,
  fetchTimeframeData,
  getCachedData,
  TIMEFRAMES,
  dataCache,

  // Rule-based functions (legacy)
  generateTradingSignal,
  analyzeWithGPT,

  // AI Vision functions (new)
  analyzeChartWithAIVision,
  performAIVisualAnalysis,
  generateAITradingSignal,
  analyzeWithAIVision,

  // Bot functions
  runBot,
  runRuleBasedBot,
  runLegacyBot,

  // Chart handler
  chartHandler,
};

// === AI Visual Pattern Recognition ===
async function analyzeChartWithAIVision(chartBuffer, timeframe, currentPrice) {
  try {
    console.log(`🔍 Analyzing ${timeframe} chart with AI vision...`);

    // Convert buffer to base64 for OpenAI Vision API
    const base64Image = chartBuffer.toString("base64");

    const prompt = `Bạn là chuyên gia phân tích kỹ thuật giáo dục. Hãy phân tích biểu đồ giá ${timeframe} của ${SYMBOL} cho mục đích học tập và nghiên cứu.

BỐI CẢNH PHÂN TÍCH:
- Mã tài sản: ${SYMBOL}
- Khung thời gian: ${timeframe}
- Giá tham khảo: ${currentPrice}

MỤC TIÊU PHÂN TÍCH GIÁO DỤC:
1. XU HƯỚNG GIÁ: Nhận dạng hướng chuyển động giá (tăng, giảm, hoặc ngang)
2. MÔ HÌNH KỸ THUẬT: Tìm các mô hình biểu đồ cổ điển để học tập
3. CẤU TRÚC NẾN: Phân tích các hình thành nến cho mục đích giáo dục
4. VÙNG GIÁ QUAN TRỌNG: Xác định các mức giá có ý nghĩa lịch sử
5. ĐƯỜNG TRUNG BÌNH: Quan sát mối quan hệ giá với các đường EMA (Xanh=EMA20, Cam=EMA50, Đỏ=EMA200)
6. KHỐI LƯỢNG: Nhận xét về hoạt động giao dịch nếu quan sát được
7. ĐỘNG NĂNG: Đánh giá sự thay đổi trong động năng giá

Đây là phân tích cho mục đích giáo dục và nghiên cứu, không phải lời khuyên đầu tư.

TRẢ LỜI theo định dạng JSON CHÍNH XÁC này:
{
  "trend": {
    "direction": "BULLISH|BEARISH|SIDEWAYS",
    "strength": 1-10,
    "confidence": 1-10,
    "reasoning": "Brief explanation of trend analysis"
  },
  "patterns": {
    "chart_patterns": ["list of identified chart patterns"],
    "candlestick_patterns": ["list of candlestick patterns"],
    "pattern_significance": "HIGH|MEDIUM|LOW"
  },
  "levels": {
    "support": [list of support price levels],
    "resistance": [list of resistance price levels],
    "key_level_proximity": "How close is current price to key levels"
  },
  "moving_averages": {
    "price_vs_ema20": "ABOVE|BELOW|AT",
    "price_vs_ema50": "ABOVE|BELOW|AT",
    "price_vs_ema200": "ABOVE|BELOW|AT",
    "ema_alignment": "BULLISH|BEARISH|MIXED",
    "ema_analysis": "Brief analysis of EMA positioning"
  },
  "momentum": {
    "direction": "INCREASING|DECREASING|NEUTRAL",
    "strength": 1-10,
    "momentum_analysis": "Analysis of momentum indicators"
  },
  "trading_signal": {
    "signal": "STRONG_BUY|BUY|HOLD|SELL|STRONG_SELL",
    "entry_zone": "Suggested entry price range",
    "stop_loss": "Suggested stop loss level",
    "take_profit": "Suggested take profit levels",
    "risk_reward": "Risk to reward ratio",
    "signal_reasoning": "Why this signal was generated"
  },
  "overall_assessment": {
    "market_condition": "TRENDING|RANGING|VOLATILE|CONSOLIDATING",
    "trade_quality": "HIGH|MEDIUM|LOW",
    "confidence_score": 1-10,
    "key_insights": "Most important observations"
  }
}

QUAN TRỌNG:
- Chỉ trả lời bằng JSON hợp lệ
- Bắt đầu bằng { và kết thúc bằng }
- Không có văn bản giải thích bên ngoài JSON
- Không sử dụng markdown hoặc code blocks
- Đảm bảo tất cả string được đặt trong dấu ngoặc kép`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content:
            "You are a technical analysis expert. You MUST respond ONLY with valid JSON. Do not include any explanatory text, apologies, or markdown formatting. Start your response with { and end with }. If you cannot analyze the chart, still respond with valid JSON using placeholder values.",
        },
        {
          role: "user",
          content: [
            { type: "text", text: prompt },
            {
              type: "image_url",
              image_url: {
                url: `data:image/png;base64,${base64Image}`,
                detail: "high",
              },
            },
          ],
        },
      ],
      max_tokens: 2000,
      temperature: 0.1, // Low temperature for consistent analysis
    });

    const analysisText = response.choices[0].message.content;

    // Clean and parse JSON response
    let analysis;
    try {
      // Try to extract JSON from the response
      let jsonText = analysisText.trim();

      console.log(
        `🔍 Raw AI response for ${timeframe}: ${analysisText.substring(
          0,
          100
        )}...`
      );

      // Look for JSON block if wrapped in markdown
      const jsonMatch = jsonText.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonText = jsonMatch[1].trim();
        console.log(`📝 Found JSON in markdown block for ${timeframe}`);
      }

      // Look for JSON block without language specification
      const codeBlockMatch = jsonText.match(/```\s*([\s\S]*?)\s*```/);
      if (codeBlockMatch && !jsonMatch) {
        const potentialJson = codeBlockMatch[1].trim();
        if (potentialJson.startsWith("{") && potentialJson.endsWith("}")) {
          jsonText = potentialJson;
          console.log(`📝 Found JSON in code block for ${timeframe}`);
        }
      }

      // Remove any leading/trailing non-JSON text
      const jsonStart = jsonText.indexOf("{");
      const jsonEnd = jsonText.lastIndexOf("}");

      if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
        jsonText = jsonText.substring(jsonStart, jsonEnd + 1);
        console.log(`🔧 Extracted JSON substring for ${timeframe}`);
      } else {
        throw new Error(
          `No valid JSON structure found in response for ${timeframe}`
        );
      }

      // Additional cleaning for common AI response issues
      jsonText = jsonText
        .replace(/^\s*I'm sorry[^{]*/, "") // Remove "I'm sorry" prefixes
        .replace(/^[^{]*/, "") // Remove any text before first {
        .replace(/}[^}]*$/, "}") // Remove any text after last }
        .trim();

      if (!jsonText.startsWith("{") || !jsonText.endsWith("}")) {
        throw new Error(
          `Invalid JSON format for ${timeframe}: doesn't start with { or end with }`
        );
      }

      console.log(`🔍 Attempting to parse cleaned JSON for ${timeframe}...`);

      // Try to repair common JSON issues before parsing
      jsonText = repairCommonJsonIssues(jsonText);

      analysis = JSON.parse(jsonText);

      // Validate required fields
      if (
        !analysis.trend ||
        !analysis.trading_signal ||
        !analysis.overall_assessment
      ) {
        throw new Error("Missing required fields in AI analysis");
      }

      console.log(`✅ Successfully parsed JSON for ${timeframe}`);
    } catch (parseError) {
      console.error(
        `❌ Failed to parse AI analysis for ${timeframe}:`,
        parseError.message
      );
      console.error(
        `Raw response (first 300 chars): ${analysisText.substring(0, 300)}...`
      );

      // Log the cleaned JSON attempt for debugging
      if (parseError.message.includes("JSON")) {
        const jsonStart = analysisText.indexOf("{");
        const jsonEnd = analysisText.lastIndexOf("}");
        if (jsonStart !== -1 && jsonEnd !== -1) {
          const attemptedJson = analysisText.substring(jsonStart, jsonEnd + 1);
          console.error(
            `Attempted JSON parse: ${attemptedJson.substring(0, 200)}...`
          );
        }
      }

      // Try to create analysis from text if JSON parsing fails
      analysis = createAnalysisFromText(analysisText, timeframe);
    }

    console.log(`✅ AI analysis completed for ${timeframe}`);
    return analysis;
  } catch (error) {
    console.error(`❌ AI vision analysis failed for ${timeframe}:`, error);
    return createFallbackAnalysis(timeframe);
  }
}

function repairCommonJsonIssues(jsonText) {
  try {
    // Fix common JSON formatting issues
    return (
      jsonText
        // Fix trailing commas
        .replace(/,(\s*[}\]])/g, "$1")
        // Fix missing quotes around property names
        .replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":')
        // Fix single quotes to double quotes
        .replace(/'/g, '"')
        // Fix escaped quotes that shouldn't be escaped
        .replace(/\\"/g, '"')
        // Fix missing commas between properties
        .replace(/"\s*\n\s*"/g, '",\n"')
        // Remove any control characters
        .replace(/[\x00-\x1F\x7F]/g, "")
    );
  } catch (error) {
    console.warn("JSON repair failed:", error.message);
    return jsonText;
  }
}

function createAnalysisFromText(responseText, timeframe) {
  console.log(
    `🔄 Attempting to extract analysis from text for ${timeframe}...`
  );
  console.log(`Text response preview: ${responseText.substring(0, 150)}...`);

  // Check if response contains apology or error message
  const text = responseText.toLowerCase();
  const isApologyResponse =
    text.includes("i'm sorry") ||
    text.includes("i cannot") ||
    text.includes("unable to") ||
    text.includes("apologize");

  if (isApologyResponse) {
    console.log(
      `⚠️ AI provided apology response for ${timeframe}, using neutral fallback`
    );
    return createFallbackAnalysis(timeframe);
  }

  // Try to extract key information from text response

  // Determine trend from text
  let trendDirection = "SIDEWAYS";
  let trendStrength = 5;
  let confidence = 5;

  if (
    text.includes("bullish") ||
    text.includes("tăng") ||
    text.includes("uptrend") ||
    text.includes("up trend") ||
    text.includes("rising") ||
    text.includes("increasing") ||
    text.includes("positive") ||
    text.includes("strong buy")
  ) {
    trendDirection = "BULLISH";
    trendStrength = 7;
    confidence = 6;
  } else if (
    text.includes("bearish") ||
    text.includes("giảm") ||
    text.includes("downtrend") ||
    text.includes("down trend") ||
    text.includes("falling") ||
    text.includes("decreasing") ||
    text.includes("negative") ||
    text.includes("strong sell")
  ) {
    trendDirection = "BEARISH";
    trendStrength = 7;
    confidence = 6;
  }

  // Determine signal from text
  let signal = "HOLD";
  if (
    text.includes("buy") ||
    text.includes("long") ||
    text.includes("mua") ||
    text.includes("purchase") ||
    text.includes("enter long") ||
    text.includes("strong_buy") ||
    text.includes("strong buy")
  ) {
    signal = text.includes("strong") ? "STRONG_BUY" : "BUY";
  } else if (
    text.includes("sell") ||
    text.includes("short") ||
    text.includes("bán") ||
    text.includes("exit") ||
    text.includes("enter short") ||
    text.includes("strong_sell") ||
    text.includes("strong sell")
  ) {
    signal = text.includes("strong") ? "STRONG_SELL" : "SELL";
  }

  return {
    trend: {
      direction: trendDirection,
      strength: trendStrength,
      confidence: confidence,
      reasoning: `Extracted from text analysis for ${timeframe}`,
    },
    patterns: {
      chart_patterns: [],
      candlestick_patterns: [],
      pattern_significance: "LOW",
    },
    levels: {
      support: [],
      resistance: [],
      key_level_proximity: "Unknown",
    },
    moving_averages: {
      price_vs_ema20: "AT",
      price_vs_ema50: "AT",
      price_vs_ema200: "AT",
      ema_alignment: "MIXED",
      ema_analysis: "Text analysis - limited data",
    },
    momentum: {
      direction: "NEUTRAL",
      strength: 5,
      momentum_analysis: "Text analysis - limited data",
    },
    trading_signal: {
      signal: signal,
      entry_zone: "Market price",
      stop_loss: "Use 2% rule",
      take_profit: "Use 2:1 RR",
      risk_reward: "1:2",
      signal_reasoning: "Extracted from text analysis",
    },
    overall_assessment: {
      market_condition: "RANGING",
      trade_quality: "LOW",
      confidence_score: confidence,
      key_insights: `Text-based analysis for ${timeframe} - JSON parsing failed`,
    },
  };
}

function createFallbackAnalysis(timeframe) {
  return {
    trend: {
      direction: "SIDEWAYS",
      strength: 5,
      confidence: 3,
      reasoning: `AI analysis unavailable for ${timeframe}`,
    },
    patterns: {
      chart_patterns: [],
      candlestick_patterns: [],
      pattern_significance: "LOW",
    },
    levels: {
      support: [],
      resistance: [],
      key_level_proximity: "Unknown",
    },
    moving_averages: {
      price_vs_ema20: "AT",
      price_vs_ema50: "AT",
      price_vs_ema200: "AT",
      ema_alignment: "MIXED",
      ema_analysis: "Analysis unavailable",
    },
    momentum: {
      direction: "NEUTRAL",
      strength: 5,
      momentum_analysis: "Analysis unavailable",
    },
    trading_signal: {
      signal: "HOLD",
      entry_zone: "N/A",
      stop_loss: "N/A",
      take_profit: "N/A",
      risk_reward: "N/A",
      signal_reasoning: "AI analysis failed",
    },
    overall_assessment: {
      market_condition: "RANGING",
      trade_quality: "LOW",
      confidence_score: 1,
      key_insights: "AI vision analysis unavailable",
    },
  };
}

// === AI-Powered Multi-Timeframe Analysis ===
async function performAIVisualAnalysis(multiTimeframeAnalysis) {
  try {
    console.log("🤖 Starting AI-powered visual chart analysis...");

    // Generate clean charts for AI analysis
    const charts = await chartHandler.generateMultiTimeframeChartsForAI(
      multiTimeframeAnalysis
    );

    // Analyze each timeframe with AI vision
    const aiAnalysis = {};
    const analysisPromises = [];

    for (const [timeframe, chartData] of Object.entries(charts)) {
      const currentPrice =
        multiTimeframeAnalysis[timeframe].candles.at(-1).close;

      const promise = analyzeChartWithAIVision(
        chartData.buffer,
        timeframe,
        currentPrice
      )
        .then((analysis) => {
          aiAnalysis[timeframe] = analysis;
          console.log(`✅ Successfully analyzed ${timeframe} chart`);
        })
        .catch((error) => {
          console.error(
            `❌ Failed to analyze ${timeframe} chart:`,
            error.message
          );
          aiAnalysis[timeframe] = createFallbackAnalysis(timeframe);
        });

      analysisPromises.push(promise);
    }

    await Promise.all(analysisPromises);

    console.log("✅ AI visual analysis completed for all timeframes");
    return aiAnalysis;
  } catch (error) {
    console.error("❌ AI visual analysis failed:", error);
    throw error;
  }
}

// === AI-Powered Trading Signal Generation (Replaces Rule-Based Logic) ===
async function generateAITradingSignal(aiAnalysis, multiTimeframeAnalysis) {
  try {
    console.log("🎯 Generating AI-powered trading signal...");

    const { "4h": ai4h, "1h": ai1h, "15m": ai15m } = aiAnalysis;
    const currentPrice = multiTimeframeAnalysis["15m"].candles.at(-1).close;

    // Tạo prompt phân tích toàn diện cho mục đích giáo dục
    const multiTimeframePrompt = `Bạn là chuyên gia phân tích kỹ thuật giáo dục. Hãy tạo một phân tích giáo dục dựa trên dữ liệu đa khung thời gian cho mục đích học tập và nghiên cứu.

KẾT QUẢ PHÂN TÍCH AI ĐA KHUNG THỜI GIAN:

PHÂN TÍCH KHUNG 4H:
- Xu hướng: ${ai4h.trend.direction} (Sức mạnh: ${
      ai4h.trend.strength
    }/10, Độ tin cậy: ${ai4h.trend.confidence}/10)
- Mô hình: ${ai4h.patterns.chart_patterns.join(", ") || "Không xác định được"}
- Tín hiệu: ${ai4h.trading_signal.signal}
- Nhận định chính: ${ai4h.overall_assessment.key_insights}

PHÂN TÍCH KHUNG 1H:
- Xu hướng: ${ai1h.trend.direction} (Sức mạnh: ${
      ai1h.trend.strength
    }/10, Độ tin cậy: ${ai1h.trend.confidence}/10)
- Mô hình: ${ai1h.patterns.chart_patterns.join(", ") || "Không xác định được"}
- Tín hiệu: ${ai1h.trading_signal.signal}
- Nhận định chính: ${ai1h.overall_assessment.key_insights}

PHÂN TÍCH KHUNG 15M:
- Xu hướng: ${ai15m.trend.direction} (Sức mạnh: ${
      ai15m.trend.strength
    }/10, Độ tin cậy: ${ai15m.trend.confidence}/10)
- Mô hình: ${ai15m.patterns.chart_patterns.join(", ") || "Không xác định được"}
- Tín hiệu: ${ai15m.trading_signal.signal}
- Nhận định chính: ${ai15m.overall_assessment.key_insights}

DỮ LIỆU THỊ TRƯỜNG HIỆN TẠI:
- Mã: ${SYMBOL}
- Giá hiện tại: ${currentPrice}
- Hỗ trợ/Kháng cự 4H: ${ai4h.levels.support.join(
      ", "
    )} / ${ai4h.levels.resistance.join(", ")}
- Hỗ trợ/Kháng cự 1H: ${ai1h.levels.support.join(
      ", "
    )} / ${ai1h.levels.resistance.join(", ")}
- Hỗ trợ/Kháng cự 15M: ${ai15m.levels.support.join(
      ", "
    )} / ${ai15m.levels.resistance.join(", ")}

KHUNG PHÂN TÍCH GIÁO DỤC:
1. Các khung thời gian cao hơn (4H, 1H) cho thấy xu hướng tổng thể
2. Khung 15M cung cấp chi tiết về timing và entry points
3. Sự thống nhất giữa các khung thời gian tạo ra tín hiệu mạnh hơn
4. Quản lý rủi ro là yếu tố quan trọng trong mọi chiến lược

Đây là phân tích cho mục đích giáo dục, không phải lời khuyên đầu tư.

TRẢ LỜI với phân tích giáo dục toàn diện theo định dạng JSON CHÍNH XÁC này:
{
  "major_trend": {
    "direction": "BULLISH|BEARISH|SIDEWAYS",
    "strength": 1-10,
    "confidence": 1-10,
    "reasoning": "Analysis of 4H and 1H alignment"
  },
  "timeframe_alignment": {
    "alignment_score": 1-10,
    "alignment_type": "FULLY_ALIGNED|PARTIALLY_ALIGNED|CONFLICTING",
    "alignment_analysis": "How well do all timeframes align"
  },
  "trading_recommendation": {
    "action": "STRONG_BUY|BUY|HOLD|SELL|STRONG_SELL|NO_TRADE",
    "confidence": 1-10,
    "entry_price": "Specific entry price or range",
    "stop_loss": "Specific stop loss level",
    "take_profit_1": "First take profit target",
    "take_profit_2": "Second take profit target",
    "risk_reward_ratio": "Risk to reward ratio",
    "position_size": "Suggested position size (SMALL|MEDIUM|LARGE)",
    "reasoning": "Detailed reasoning for this recommendation"
  },
  "risk_assessment": {
    "risk_level": "LOW|MEDIUM|HIGH",
    "key_risks": ["List of main risks"],
    "invalidation_level": "Price level that invalidates the setup",
    "market_conditions": "Current market environment assessment"
  },
  "execution_plan": {
    "entry_strategy": "How to enter the position",
    "exit_strategy": "How to manage the position",
    "monitoring_points": ["Key levels to watch"],
    "time_horizon": "Expected trade duration"
  },
  "overall_assessment": {
    "trade_quality": "EXCELLENT|GOOD|FAIR|POOR",
    "market_opportunity": 1-10,
    "final_confidence": 1-10,
    "key_message": "Most important takeaway for the trader"
  }
}

QUAN TRỌNG: Chỉ trả lời bằng JSON hợp lệ. Xem xét tất cả khung thời gian nhưng ưu tiên xu hướng khung thời gian cao hơn cho hướng chính.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content:
            "Bạn là chuyên gia phân tích kỹ thuật giáo dục với hơn 20 năm kinh nghiệm. Tạo phân tích giáo dục thận trọng, có lý lẽ dựa trên đa khung thời gian cho mục đích học tập. Luôn nhấn mạnh quản lý rủi ro và tính chất giáo dục của phân tích.",
        },
        {
          role: "user",
          content: multiTimeframePrompt,
        },
      ],
      max_tokens: 2000,
      temperature: 0.1,
    });

    const signalText = response.choices[0].message.content;

    let tradingSignal;
    try {
      // Clean and parse JSON response
      let jsonText = signalText.trim();

      // Look for JSON block if wrapped in markdown
      const jsonMatch = jsonText.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonText = jsonMatch[1].trim();
      }

      // Remove any leading/trailing non-JSON text
      const jsonStart = jsonText.indexOf("{");
      const jsonEnd = jsonText.lastIndexOf("}");

      if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
        jsonText = jsonText.substring(jsonStart, jsonEnd + 1);
      }

      console.log("🔍 Attempting to parse AI trading signal...");
      tradingSignal = JSON.parse(jsonText);

      // Validate required fields
      if (!tradingSignal.major_trend || !tradingSignal.trading_recommendation) {
        throw new Error("Missing required fields in trading signal");
      }
    } catch (parseError) {
      console.error("❌ Failed to parse AI trading signal:", parseError);
      console.error(`Raw response: ${signalText.substring(0, 200)}...`);
      tradingSignal = createFallbackTradingSignal();
    }

    // Add metadata
    tradingSignal.metadata = {
      analysis_type: "AI_VISUAL",
      timestamp: new Date().toISOString(),
      symbol: SYMBOL,
      current_price: currentPrice,
      timeframes_analyzed: ["4h", "1h", "15m"],
    };

    console.log("✅ AI trading signal generated successfully");
    return tradingSignal;
  } catch (error) {
    console.error("❌ AI trading signal generation failed:", error);
    return createFallbackTradingSignal();
  }
}

function createFallbackTradingSignal() {
  return {
    major_trend: {
      direction: "SIDEWAYS",
      strength: 5,
      confidence: 3,
      reasoning: "AI analysis unavailable",
    },
    timeframe_alignment: {
      alignment_score: 5,
      alignment_type: "CONFLICTING",
      alignment_analysis: "Unable to determine alignment",
    },
    trading_recommendation: {
      action: "NO_TRADE",
      confidence: 1,
      entry_price: "N/A",
      stop_loss: "N/A",
      take_profit_1: "N/A",
      take_profit_2: "N/A",
      risk_reward_ratio: "N/A",
      position_size: "SMALL",
      reasoning: "AI analysis failed - avoiding risk",
    },
    risk_assessment: {
      risk_level: "HIGH",
      key_risks: ["AI analysis unavailable"],
      invalidation_level: "N/A",
      market_conditions: "Unknown",
    },
    execution_plan: {
      entry_strategy: "Wait for manual analysis",
      exit_strategy: "N/A",
      monitoring_points: [],
      time_horizon: "N/A",
    },
    overall_assessment: {
      trade_quality: "POOR",
      market_opportunity: 1,
      final_confidence: 1,
      key_message: "AI vision analysis failed - manual review required",
    },
    metadata: {
      analysis_type: "FALLBACK",
      timestamp: new Date().toISOString(),
      symbol: SYMBOL,
      current_price: 0,
      timeframes_analyzed: [],
    },
  };
}

// === Enhanced GPT Analysis with AI Vision Results ===
async function analyzeWithAIVision(aiAnalysis, aiTradingSignal) {
  try {
    const { "15m": ai15m, "1h": ai1h, "4h": ai4h } = aiAnalysis;
    const currentPrice = aiTradingSignal.metadata.current_price;

    // Format AI analysis for human-readable output
    const formatTrend = (trend) => {
      const trendEmoji = { BULLISH: "🟢", BEARISH: "🔴", SIDEWAYS: "⚪" };
      return `${trendEmoji[trend.direction]} ${trend.direction}`;
    };

    const formatSignal = (action) => {
      const signalEmoji = {
        STRONG_BUY: "🟢🟢",
        BUY: "🟢",
        STRONG_SELL: "🔴🔴",
        SELL: "🔴",
        HOLD: "⚪",
        NO_TRADE: "⚪",
      };
      return `${signalEmoji[action] || "⚪"} ${action}`;
    };

    const text = `Bạn là chuyên gia phân tích kỹ thuật giáo dục tạo báo cáo học tập toàn diện dựa trên phân tích biểu đồ AI cho mục đích giáo dục.

KẾT QUẢ PHÂN TÍCH HÌNH ẢNH AI:

XU HƯỚNG ĐA KHUNG THỜI GIAN:
- 4H: ${formatTrend(ai4h.trend)} (Độ tin cậy: ${ai4h.trend.confidence}/10)
- 1H: ${formatTrend(ai1h.trend)} (Độ tin cậy: ${ai1h.trend.confidence}/10)
- 15M: ${formatTrend(ai15m.trend)} (Độ tin cậy: ${ai15m.trend.confidence}/10)

MÔ HÌNH BIỂU ĐỒ ĐƯỢC XÁC ĐỊNH:
- Mô hình 4H: ${ai4h.patterns.chart_patterns.join(", ") || "Không có"}
- Mô hình 1H: ${ai1h.patterns.chart_patterns.join(", ") || "Không có"}
- Mô hình 15M: ${ai15m.patterns.chart_patterns.join(", ") || "Không có"}

KHUYẾN NGHỊ GIAO DỊCH AI:
- Hành động: ${formatSignal(aiTradingSignal.trading_recommendation.action)}
- Độ tin cậy: ${aiTradingSignal.trading_recommendation.confidence}/10
- Vào lệnh: ${aiTradingSignal.trading_recommendation.entry_price}
- Cắt lỗ: ${aiTradingSignal.trading_recommendation.stop_loss}
- Chốt lời: ${aiTradingSignal.trading_recommendation.take_profit_1}
- Tỷ lệ R/R: ${aiTradingSignal.trading_recommendation.risk_reward_ratio}

ĐIỀU KIỆN THỊ TRƯỜNG:
- Xu hướng chính: ${formatTrend(aiTradingSignal.major_trend)} (Sức mạnh: ${
      aiTradingSignal.major_trend.strength
    }/10)
- Sự thống nhất: ${aiTradingSignal.timeframe_alignment.alignment_type}
- Chất lượng giao dịch: ${aiTradingSignal.overall_assessment.trade_quality}

Tạo báo cáo phân tích giáo dục theo định dạng HTML CHÍNH XÁC này (chỉ cho mục đích học tập):

<b>🤖 PHÂN TÍCH HÌNH ẢNH AI:</b>
• <b>Xu hướng chính:</b> [${
      aiTradingSignal.major_trend.direction
    }] - [Lý do ngắn gọn]
• <b>Đồng bộ khung thời gian:</b> [Trạng thái và chất lượng thống nhất]
• <b>Mô hình chính:</b> [Các mô hình quan trọng nhất được tìm thấy]

<b>🎯 TÍN HIỆU GIAO DỊCH AI:</b>
• <b>Hành động:</b> [${aiTradingSignal.trading_recommendation.action}]
• <b>Vùng vào lệnh:</b> [Giá/khoảng vào lệnh]
• <b>Quản lý rủi ro:</b> SL ${
      aiTradingSignal.trading_recommendation.stop_loss
    } | TP ${aiTradingSignal.trading_recommendation.take_profit_1}
• <b>Tỷ lệ R/R:</b> ${aiTradingSignal.trading_recommendation.risk_reward_ratio}

<b>💡 Độ tin cậy AI:</b> ${
      aiTradingSignal.overall_assessment.final_confidence
    }/10

<b>🔍 Nhận định chính:</b> ${aiTradingSignal.overall_assessment.key_message}

QUAN TRỌNG: Chỉ sử dụng thẻ HTML <b></b>. Ngắn gọn và mang tính giáo dục. Tối đa 400 ký tự. Luôn nhấn mạnh tính chất giáo dục.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content:
            "Bạn là chuyên gia phân tích kỹ thuật giáo dục. Tạo báo cáo phân tích ngắn gọn, mang tính giáo dục dựa trên phân tích AI cho mục đích học tập. Chỉ sử dụng thẻ HTML <b></b>, không dùng markdown. Luôn nhấn mạnh tính chất giáo dục và không phải lời khuyên đầu tư.",
        },
        {
          role: "user",
          content: text,
        },
      ],
      max_tokens: 800,
      temperature: 0.1,
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.error("❌ Enhanced GPT analysis failed:", error);
    return `<b>⚠️ LỖI PHÂN TÍCH AI</b>\n\nPhân tích hình ảnh AI gặp lỗi. Vui lòng kiểm tra system logs.\n\n<b>Trạng thái:</b> Cần xem xét thủ công`;
  }
}
